{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0">{{ supplier.name }}</h2>
                        <div>
                            <a href="{{ url_for('inventory.edit_supplier', id=supplier.id) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            <a href="{{ url_for('inventory.suppliers') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informations Générales</h5>
                            <table class="table">
                                <tr>
                                    <th>Nom</th>
                                    <td>{{ supplier.name }}</td>
                                </tr>
                                <tr>
                                    <th>Catégorie</th>
                                    <td>
                                        {% if supplier.category %}
                                            <span class="badge" style="background-color: {{ supplier.category.color }};">
                                                <i class="{{ supplier.category.icon }}"></i> {{ supplier.category.name }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">Sans catégorie</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Note</th>
                                    <td>
                                        {% if supplier.rating > 0 %}
                                            <span class="text-warning">{{ supplier.display_rating }}</span>
                                        {% else %}
                                            <span class="text-muted">Non noté</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Statut</th>
                                    <td>
                                        {% if supplier.is_active %}
                                            <span class="badge bg-success">Actif</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Informations de Contact</h5>
                            <table class="table">
                                <tr>
                                    <th>Contact</th>
                                    <td>{{ supplier.contact_name or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>
                                        {% if supplier.email %}
                                            <a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Téléphone</th>
                                    <td>
                                        {% if supplier.phone %}
                                            <a href="tel:{{ supplier.phone }}">{{ supplier.phone }}</a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>Site web</th>
                                    <td>
                                        {% if supplier.website %}
                                            <a href="{{ supplier.website }}" target="_blank">
                                                {{ supplier.website }} <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informations Commerciales</h5>
                            <table class="table">
                                <tr>
                                    <th>N° TVA/SIRET</th>
                                    <td>{{ supplier.tax_id or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Conditions de paiement</th>
                                    <td>{{ supplier.payment_terms or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Adresse</h5>
                            {% if supplier.address %}
                                <div class="border p-3 rounded">
                                    {{ supplier.address|nl2br }}
                                </div>
                            {% else %}
                                <p class="text-muted">Aucune adresse renseignée</p>
                            {% endif %}
                        </div>
                    </div>

                    {% if supplier.notes %}
                    <div class="row">
                        <div class="col-12">
                            <h5>Notes</h5>
                            <div class="alert alert-info">
                                {{ supplier.notes|nl2br }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer text-muted">
                    <small>
                        Créé le {{ supplier.created_at.strftime('%d/%m/%Y à %H:%M') }}
                        {% if supplier.updated_at != supplier.created_at %}
                            • Modifié le {{ supplier.updated_at.strftime('%d/%m/%Y à %H:%M') }}
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Actions rapides -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Actions Rapides</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('inventory.edit_supplier', id=supplier.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Modifier les informations
                        </a>
                        {% if supplier.email %}
                        <a href="mailto:{{ supplier.email }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope"></i> Envoyer un email
                        </a>
                        {% endif %}
                        {% if supplier.phone %}
                        <a href="tel:{{ supplier.phone }}" class="btn btn-outline-success">
                            <i class="fas fa-phone"></i> Appeler
                        </a>
                        {% endif %}
                        {% if supplier.website %}
                        <a href="{{ supplier.website }}" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-globe"></i> Visiter le site web
                        </a>
                        {% endif %}
                        <a href="{{ url_for('inventory.supplier_contacts', supplier_id=supplier.id) }}"
                           class="btn btn-outline-secondary">
                            <i class="fas fa-history"></i> Historique des contacts
                        </a>
                        <a href="{{ url_for('inventory.add_supplier_contact', supplier_id=supplier.id) }}"
                           class="btn btn-success">
                            <i class="fas fa-plus"></i> Ajouter un contact
                        </a>
                        <a href="{{ url_for('inventory.pending_supplier_contacts') }}"
                           class="btn btn-outline-warning">
                            <i class="fas fa-clock"></i> Contacts en attente
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistiques -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Statistiques</h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h3 class="text-primary">0</h3>
                            <p class="mb-0">Commandes passées</p>
                        </div>
                        <div class="mb-3">
                            <h3 class="text-success">0 €</h3>
                            <p class="mb-0">Total des achats</p>
                        </div>
                        <div>
                            <h3 class="text-info">0</h3>
                            <p class="mb-0">Produits fournis</p>
                        </div>
                    </div>
                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Les statistiques seront disponibles après l'implémentation du système de commandes.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
