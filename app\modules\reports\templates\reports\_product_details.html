{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Détails du produit</h1>
        <a href="{{ url_for('reports.products') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour aux rapports
        </a>
    </div>

<!-- Product Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <h6 class="font-weight-bold">Informations générales</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Nom</th>
                <td>{{ product.name }}</td>
            </tr>
            <tr>
                <th>Catégorie</th>
                <td>
                    {% if product.category %}
                    <span class="badge" style="background-color: {{ product.category.color if product.category.color else '#6c757d' }}">
                        {{ product.category.name }}
                    </span>
                    {% else %}
                    <span class="text-muted">Sans catégorie</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <th>Prix</th>
                <td>{{ product.price|format_currency }}</td>
            </tr>
            {% if product.cost_price %}
            <tr>
                <th>Coût</th>
                <td>{{ product.cost_price|format_currency }}</td>
            </tr>
            <tr>
                <th>Marge</th>
                <td>
                    {% set margin = product.price - product.cost_price %}
                    {% set margin_rate = (margin / product.price * 100) if product.price > 0 else 0 %}
                    {{ margin|format_currency }}
                    <small class="text-muted">({{ "%.1f"|format(margin_rate) }}%)</small>
                </td>
            </tr>
            {% endif %}
            {% if product.description %}
            <tr>
                <th>Description</th>
                <td>{{ product.description }}</td>
            </tr>
            {% endif %}
        </table>
    </div>
    <div class="col-md-6">
        <h6 class="font-weight-bold">Stock</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Stock actuel</th>
                <td>{{ product.get_available_quantity() }}</td>
            </tr>
            <tr>
                <th>Stock minimum</th>
                <td>{{ product.minimum_stock }}</td>
            </tr>
            <tr>
                <th>Statut</th>
                <td>
                    {% if product.get_available_quantity() == 0 %}
                    <span class="badge bg-danger">Rupture</span>
                    {% elif product.get_available_quantity() <= product.minimum_stock %}
                    <span class="badge bg-warning">Stock faible</span>
                    {% else %}
                    <span class="badge bg-success">En stock</span>
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>
</div>

{% if stats %}
<!-- Performance -->
<div class="row">
    <div class="col-12">
        <h6 class="font-weight-bold mb-3">Performance</h6>
        <table class="table table-sm">
            <tr>
                <th style="width: 150px">Quantité vendue</th>
                <td>{{ stats.quantity_sold }}</td>
            </tr>
            <tr>
                <th>CA Total</th>
                <td>{{ stats.total_revenue|format_currency }}</td>
            </tr>
            <tr>
                <th>Part du CA</th>
                <td>{{ "%.1f"|format(stats.revenue_share) }}%</td>
            </tr>
            <tr>
                <th>Classement</th>
                <td>#{{ stats.rank }} sur {{ stats.total_products }}</td>
            </tr>
        </table>
    </div>
</div>
{% endif %}

{% if trend %}
<!-- Sales Trend -->
<div class="row mt-4">
    <div class="col-12">
        <h6 class="font-weight-bold mb-3">Évolution des ventes</h6>
        <div class="chart-area" style="height: 300px;">
            <canvas id="productSalesChart"></canvas>
        </div>
    </div>
</div>
{% endif %}

{% if trend %}
<!-- Sales Trend Chart Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM chargé pour product details');
    console.log('Données de tendance:', {{ trend|tojson }});

    // Fonction pour initialiser le graphique
    function initChart() {
        console.log('Tentative d\'initialisation du graphique');
        console.log('Chart.js disponible:', typeof Chart !== 'undefined');

        if (typeof Chart !== 'undefined') {
            const chartElement = document.getElementById('productSalesChart');
            console.log('Élément canvas trouvé:', chartElement !== null);

            if (chartElement) {
                console.log('Initialisation du graphique de tendance des ventes');
                const salesCtx = chartElement.getContext('2d');

                const trendData = {{ trend|tojson|safe }};
                console.log('Données de tendance reçues:', trendData);

                if (!trendData || !trendData.dates || !trendData.quantities) {
                    console.error('Données de tendance manquantes ou invalides');
                    chartElement.parentElement.innerHTML = '<div class="alert alert-info text-center">Aucune donnée de vente disponible pour ce produit.</div>';
                    return;
                }

                const chartData = {
                    labels: trendData.dates,
                    datasets: [{
                        label: 'Quantité vendue',
                        data: trendData.quantities,
                        borderColor: 'rgb(78, 115, 223)',
                        backgroundColor: 'rgba(78, 115, 223, 0.05)',
                        tension: 0.3,
                        fill: true,
                        pointBackgroundColor: 'rgb(78, 115, 223)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }]
                };

                console.log('Données du graphique:', chartData);

                new Chart(salesCtx, {
                    type: 'line',
                    data: chartData,
                    options: {
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
                console.log('Graphique initialisé avec succès');
            } else {
                console.error('Élément canvas productSalesChart non trouvé');
            }
        } else {
            console.error('Chart.js n\'est pas chargé');
            // Réessayer après un délai
            setTimeout(initChart, 500);
        }
    }

    // Attendre un peu puis initialiser
    setTimeout(initChart, 500);
});
</script>
{% endif %}
{% endblock %}

{% block extra_js %}
<!-- Scripts spécifiques à la page de détails du produit -->
{% endblock %}