{% if movements %}
<div class="table-responsive">
    <table class="table">
        <thead>
            <tr>
                <th>Date</th>
                <th>Type</th>
                <th>Quantité</th>
                <th>Note</th>
                <th>Utilisateur</th>
            </tr>
        </thead>
        <tbody>
            {% for movement in movements %}
            <tr>
                <td>{{ movement.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                <td>
                    {% if movement.type == 'IN' %}
                    <span class="badge bg-success">Entrée</span>
                    {% else %}
                    <span class="badge bg-danger">Sortie</span>
                    {% endif %}
                </td>
                <td>{{ movement.quantity }}</td>
                <td>{{ movement.note or '-' }}</td>
                <td>{{ movement.user.username if movement.user else '-' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if pagination and pagination.pages > 1 %}
<nav aria-label="Pagination de l'historique">
    <ul class="pagination pagination-sm justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="#" onclick="loadHistoryPage({{ item_id }}, {{ pagination.prev_num }})">Précédent</a>
        </li>
        {% endif %}

        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadHistoryPage({{ item_id }}, {{ page_num }})">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}

        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="#" onclick="loadHistoryPage({{ item_id }}, {{ pagination.next_num }})">Suivant</a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<p class="text-muted">Aucun mouvement trouvé pour cet article.</p>
{% endif %}