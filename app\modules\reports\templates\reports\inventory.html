{% extends "base.html" %}

{% block styles %}
<style>
.chart-area, .chart-pie {
    position: relative;
    height: 400px;
    margin: 20px 0;
}
</style>
{% endblock %}

{% block title %}Inventaire{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-file-export"></i> Exporter
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportInventoryReport('csv')">
                        <i class="fas fa-file-csv"></i> CSV
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportInventoryReport('pdf')">
                        <i class="fas fa-file-pdf"></i> PDF
                    </a></li>
                </ul>
            </div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                <i class="fas fa-filter"></i> Filtrer
            </button>
        </div>
    </div>

    <!-- Filter Row -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <h6 class="card-title mb-3">Filtrer par type d'articles</h6>
                    <form id="inventoryFilterForm" method="GET">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="item_types" value="products_no_recipe" id="products_no_recipe"
                                           {% if not request.args.getlist('item_types') or 'products_no_recipe' in request.args.getlist('item_types') %}checked{% endif %}>
                                    <label class="form-check-label" for="products_no_recipe">
                                        <i class="fas fa-box text-primary"></i> Produits sans recette
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="item_types" value="products_with_recipe" id="products_with_recipe"
                                           {% if not request.args.getlist('item_types') or 'products_with_recipe' in request.args.getlist('item_types') %}checked{% endif %}>
                                    <label class="form-check-label" for="products_with_recipe">
                                        <i class="fas fa-utensils text-success"></i> Produits avec recette
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="item_types" value="ingredients" id="ingredients"
                                           {% if not request.args.getlist('item_types') or 'ingredients' in request.args.getlist('item_types') %}checked{% endif %}>
                                    <label class="form-check-label" for="ingredients">
                                        <i class="fas fa-leaf text-warning"></i> Ingrédients
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Appliquer le filtre
                                </button>
                                <a href="{{ url_for('reports.inventory') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo"></i> Réinitialiser
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards Row -->
    <div class="row">
        <!-- Total Items Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total des articles</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ items_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Stock faible</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Out of Stock Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Rupture de stock</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ out_of_stock_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Value Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Valeur totale</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_stock_value|format_currency }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">État du stock</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="inventoryTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Catégorie</th>
                            <th>Stock actuel</th>
                            <th>Stock minimum</th>
                            <th>Valeur unitaire</th>
                            <th>Valeur totale</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items.items %}
                        <tr>
                            <td>{{ item.name }}</td>
                            <td>{{ item.category }}</td>
                            <td>
                                {% if item.stock_quantity <= item.minimum_stock %}
                                <span class="text-danger">{{ item.stock_quantity }}</span>
                                {% else %}
                                {{ item.stock_quantity }}
                                {% endif %}
                            </td>
                            <td>{{ item.minimum_stock }}</td>
                            <td>{{ item.unit_price|format_currency }}</td>
                            <td>{{ item.total_value|format_currency }}</td>
                            <td>
                                <button type="button" class="btn btn-info btn-sm" onclick="showHistory('{{ item.id }}')">
                                    <i class="fas fa-history"></i>
                                </button>
                                <button type="button" class="btn btn-primary btn-sm" onclick="adjustStock('{{ item.id }}', '{{ item.type }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Movement History -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Historique des mouvements</h6>
        </div>
        <div class="card-body">
            {% if movement_history %}
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Type</th>
                            <th>Quantité</th>
                            <th>Date</th>
                            <th>Utilisateur</th>
                            <th>Note</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in movement_history %}
                        <tr>
                            <td>
                                {% if movement.product %}
                                    {{ movement.product.name }}
                                {% elif movement.ingredient %}
                                    {{ movement.ingredient.name }}
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.type == StockMovementType.IN %}
                                <span class="badge bg-success">Entrée</span>
                                {% else %}
                                <span class="badge bg-danger">Sortie</span>
                                {% endif %}
                            </td>
                            <td>{{ movement.quantity }}</td>
                            <td>{{ movement.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td>{% if movement.user %}{{ movement.user.username }}{% endif %}</td>
                            <td>{{ movement.note if movement.note else '' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-muted">Aucun mouvement enregistré</p>
            {% endif %}
        </div>
    </div>

    <!-- Most Active Items -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Articles les plus actifs</h6>
        </div>
        <div class="card-body">
            {% if active_items %}
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Mouvements</th>
                            <th>Activité</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in active_items %}
                        <tr>
                            <td>{{ item.name }}</td>
                            <td>{{ item.movements_count }}</td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-info" role="progressbar" 
                                         style="width: {% if max_movements > 0 %}{{ (item.movements_count / max_movements * 100)|round }}{% else %}0{% endif %}%">
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p class="text-muted">Aucune donnée disponible</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Filtrer l'inventaire</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('reports.inventory') }}" method="GET">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Catégorie</label>
                        <select name="category" class="form-select">
                            <option value="">Toutes les catégories</option>
                            {% for category in filter_categories %}
                            <option value="{{ category.id }}" {% if request.args.get('category')|int == category.id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">État du stock</label>
                        <select name="stock_status" class="form-select">
                            <option value="">Tous les états</option>
                            <option value="low" {% if request.args.get('stock_status') == 'low' %}selected{% endif %}>Stock faible</option>
                            <option value="out" {% if request.args.get('stock_status') == 'out' %}selected{% endif %}>Rupture de stock</option>
                            <option value="normal" {% if request.args.get('stock_status') == 'normal' %}selected{% endif %}>Stock normal</option>
                        </select>
                    </div>
                    <!-- Dans le modal de filtrage, après le select de période -->
                    <div class="mb-3">
                        <label class="form-label">Période</label>
                        <select name="period" class="form-select" id="periodSelect">
                            <option value="all" {% if request.args.get('period') == 'all' %}selected{% endif %}>Toute la période</option>
                            <option value="today" {% if request.args.get('period') == 'today' %}selected{% endif %}>Aujourd'hui</option>
                            <option value="week" {% if request.args.get('period') == 'week' %}selected{% endif %}>Cette semaine</option>
                            <option value="month" {% if request.args.get('period') == 'month' %}selected{% endif %}>Ce mois</option>
                            <option value="custom" {% if request.args.get('period') == 'custom' %}selected{% endif %}>Personnalisée</option>
                        </select>
                    </div>
                    <div id="customDateFields" class="mb-3" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Date de début</label>
                                <input type="date" name="start_date" class="form-control" value="{{ request.args.get('start_date', '') }}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Date de fin</label>
                                <input type="date" name="end_date" class="form-control" value="{{ request.args.get('end_date', '') }}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Appliquer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Stock Adjustment Modal -->
<div class="modal fade" id="adjustStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajuster le stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="adjustStockForm" method="POST" action="{{ url_for('reports.quick_stock_update') }}">
                {{ form.csrf_token }}
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Type de mouvement</label>
                        {{ form.movement_type(class="form-select") }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quantité</label>
                        {{ form.quantity(class="form-control", type="number", min="1") }}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Note</label>
                        {{ form.note(class="form-control", rows="3") }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- History Modal -->
<div class="modal fade" id="historyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Historique des mouvements</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="historyContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ super() }}
<!-- Charge le script JS spécifique pour les rapports -->
<script src="{{ url_for('reports.static', filename='js/reports.js') }}"></script>
{% endblock %}