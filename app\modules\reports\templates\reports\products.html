{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div>
            <button type="button" class="btn btn-outline-primary" onclick="exportProductReport()">
                <i class="fas fa-file-export"></i> Exporter
            </button>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                <i class="fas fa-filter"></i> Filtrer
            </button>
        </div>
    </div>

    <!-- Summary Cards Row -->
    <div class="row">
        <!-- Total Products Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total des produits</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_products }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Products Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Produits actifs</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_products }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Out of Stock Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Rupture de stock</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ out_of_stock }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Stock faible</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-battery-quarter fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Liste des produits</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="productsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Catégorie</th>
                            <th>Prix</th>
                            <th>Stock</th>
                            <th>Stock minimum</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td>{{ product.name }}</td>
                            <td>{{ product.category.name if product.category else "Sans catégorie" }}</td>
                            <td>{{ product.price|format_currency }}</td>
                            <td>{{ product.get_available_quantity() }}</td>
                            <td>{{ product.minimum_stock }}</td>
                            <td>
                                {% if product.get_available_quantity() == 0 %}
                                <span class="badge bg-danger">Rupture</span>
                                {% elif product.get_available_quantity() <= product.minimum_stock %}
                                <span class="badge bg-warning">Stock faible</span>
                                {% else %}
                                <span class="badge bg-success">En stock</span>
                                {% endif %}
                            </td>
                            <!-- Dans la section des actions de la table -->
                            
                            <td>
                                <a href="{{ url_for('reports.get_product_details', id=product.id) }}" class="btn btn-info btn-sm" title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('reports.edit_product', id=product.id) }}" class="btn btn-primary btn-sm" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('reports.delete_product', id=product.id) }}" class="btn btn-danger btn-sm" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Sales Trend -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Évolution des ventes</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="salesTrendChart"></canvas>
                    </div>
                    <!-- Données pour le graphique -->
                    <div class="d-none">
                        <div id="salesDates">{{ dates|tojson|safe }}</div>
                        <div id="salesValues">{{ sales_values|tojson|safe }}</div>
                    </div>
                    <!-- Message quand pas de données -->
                    {% if sales_values|sum == 0 %}
                    <div class="text-center mt-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Aucune vente enregistrée pour cette période.
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Products -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top 5 des produits</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Quantité</th>
                                    <th>Revenu</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.quantity }}</td>
                                    <td>{{ product.revenue|format_currency }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Filtrer les produits</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="filterForm" method="GET">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Période</label>
                        <select class="form-select" name="period" id="period">
                            <option value="today" {% if period == 'today' %}selected{% endif %}>Aujourd'hui</option>
                            <option value="week" {% if period == 'week' %}selected{% endif %}>Cette semaine</option>
                            <option value="month" {% if period == 'month' %}selected{% endif %}>Ce mois</option>
                            <option value="year" {% if period == 'year' %}selected{% endif %}>Cette année</option>
                            <option value="custom" {% if period == 'custom' %}selected{% endif %}>Personnalisé</option>
                        </select>
                    </div>
                    <div id="customDateRange" class="{% if period != 'custom' %}d-none{% endif %}">
                        <div class="mb-3">
                            <label class="form-label">Date de début</label>
                            <input type="date" class="form-control" name="start_date" value="{{ start_date.strftime('%Y-%m-%d') }}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date de fin</label>
                            <input type="date" class="form-control" name="end_date" value="{{ end_date.strftime('%Y-%m-%d') }}">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Catégorie</label>
                        <select class="form-select" name="category">
                            <option value="">Toutes les catégories</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Appliquer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Product Modal -->
<div class="modal fade" id="viewProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails du produit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="productDetails">
                <!-- Le contenu sera chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>

<!-- Edit Product Modal -->
<div class="modal fade" id="editProductModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier le produit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editProductForm" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label class="form-label">Nom</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Catégorie</label>
                        <select class="form-select" name="category_id">
                            <option value="">Sans catégorie</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Prix</label>
                        <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Stock minimum</label>
                        <input type="number" class="form-control" name="minimum_stock" min="0" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer ce produit ?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Supprimer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- {{ super() }} -->
<!-- DataTables -->
<!-- <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap5.min.js"></script> -->

<!-- Scripts spécifiques à la page des rapports -->
<script src="https://cdn.datatables.net/buttons/2.0.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.0.1/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.0.1/js/buttons.html5.min.js"></script>
<!-- Scripts spécifiques -->
<!-- <script src="{{ url_for('reports.static', filename='js/datatables-init.js') }}"></script> -->
<script src="{{ url_for('reports.static', filename='js/reports.js') }}"></script>
{% endblock %}

{% block scripts %}
<script>
// Fonctions pour les actions sur les produits
function viewProduct(productId) {
    console.log('Viewing product:', productId);
    fetch("{{ url_for('reports.get_product_details', id=0) }}".replace('0', productId))
        .then(response => {
            console.log('View response:', response);
            return response.text();
        })
        .then(html => {
            document.getElementById('productDetails').innerHTML = html;
            var viewModal = document.getElementById('viewProductModal');
            var bsModal = new bootstrap.Modal(viewModal);
            bsModal.show();
        })
        .catch(error => {
            console.error('Error viewing product:', error);
            alert('Erreur lors du chargement des détails du produit');
        });
}

function editProduct(productId) {
    console.log('Editing product:', productId);
    fetch("{{ url_for('reports.get_product', id=0) }}".replace('0', productId))
        .then(response => {
            console.log('Edit response:', response);
            return response.json();
        })
        .then(product => {
            const form = document.getElementById('editProductForm');
            form.action = "{{ url_for('reports.edit_product', id=0) }}".replace('0', productId);
            form.name.value = product.name;
            form.description.value = product.description || '';
            form.category_id.value = product.category_id || '';
            form.price.value = product.price;
            form.minimum_stock.value = product.minimum_stock;
            var editModal = document.getElementById('editProductModal');
            var bsModal = new bootstrap.Modal(editModal);
            bsModal.show();
        })
        .catch(error => {
            console.error('Error editing product:', error);
            alert('Erreur lors du chargement du produit');
        });
}

function deleteProduct(productId) {
    console.log('Deleting product:', productId);
    var deleteModal = document.getElementById('deleteConfirmModal');
    var bsModal = new bootstrap.Modal(deleteModal);
    
    document.getElementById('confirmDelete').onclick = function() {
        fetch("{{ url_for('reports.delete_product', id=0) }}".replace('0', productId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': "{{ csrf_token() }}"
            }
        })
        .then(response => {
            console.log('Delete response:', response);
            return response.json();
        })
        .then(data => {
            bsModal.hide();
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Une erreur est survenue');
            }
        })
        .catch(error => {
            console.error('Error deleting product:', error);
            alert('Une erreur est survenue');
            bsModal.hide();
        });
    };
    
    bsModal.show();
}

// Fonction pour l'export
function exportProductReport() {
    window.location.href = "{{ url_for('reports.export_products') }}";
}

$(document).ready(function() {
    $('#productsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        },
        "order": [[0, "asc"]]
    });

    // Handle period selection
    $('#period').change(function() {
        if ($(this).val() === 'custom') {
            $('#customDateRange').removeClass('d-none');
        } else {
            $('#customDateRange').addClass('d-none');
        }
    });

    // Initialisation du formulaire d'édition
    $('#editProductForm').on('submit', function(e) {
        e.preventDefault();
        const form = $(this);
        fetch(form.attr('action'), {
            method: 'POST',
            body: new FormData(form[0]),
            headers: {
                'X-CSRFToken': "{{ csrf_token() }}"
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Une erreur est survenue');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue');
        });
    });
});
</script>
{% endblock %}