/**
 * Initialisation sécurisée des DataTables pour les rapports
 */

// Variable globale pour éviter les doubles initialisations
window.dataTablesReportsInitialized = window.dataTablesReportsInitialized || false;

// Fonction pour initialiser les DataTables de manière sécurisée
function initializeReportsDataTables() {
    // Éviter la double initialisation
    if (window.dataTablesReportsInitialized) {
        console.log('DataTables reports déjà initialisés');
        return;
    }

    if (!$.fn.DataTable) {
        console.log('DataTables non disponible');
        return;
    }

    console.log('Initialisation des DataTables reports...');
    window.dataTablesReportsInitialized = true;

    const tables = [
        { id: '#productsTable', order: [[0, "asc"]] },
        { id: '#expensesTable', order: [[0, "desc"]] },
        { id: '#inventoryTable', order: [[0, "asc"]] }
    ];

    tables.forEach(tableConfig => {
        const table = $(tableConfig.id);
        if (table.length) {
            // Détruire la table existante si elle existe
            if ($.fn.DataTable.isDataTable(tableConfig.id)) {
                console.log(`Destruction de la table existante ${tableConfig.id}`);
                try {
                    $(tableConfig.id).DataTable().destroy();
                } catch (e) {
                    console.warn(`Erreur lors de la destruction de ${tableConfig.id}:`, e);
                }
            }

            console.log(`Initialisation de la table ${tableConfig.id}`);
            
            try {
                table.DataTable({
                    "destroy": true, // Permettre la destruction/recréation
                    "language": {
                        "sProcessing": "Traitement en cours...",
                        "sSearch": "Rechercher :",
                        "sLengthMenu": "Afficher _MENU_ éléments",
                        "sInfo": "Affichage de l'élément _START_ à _END_ sur _TOTAL_ éléments",
                        "sInfoEmpty": "Affichage de l'élément 0 à 0 sur 0 élément",
                        "sInfoFiltered": "(filtré de _MAX_ éléments au total)",
                        "sInfoPostFix": "",
                        "sLoadingRecords": "Chargement en cours...",
                        "sZeroRecords": "Aucun élément à afficher",
                        "sEmptyTable": "Aucune donnée disponible dans le tableau",
                        "oPaginate": {
                            "sFirst": "Premier",
                            "sPrevious": "Précédent",
                            "sNext": "Suivant",
                            "sLast": "Dernier"
                        },
                        "oAria": {
                            "sSortAscending": ": activer pour trier la colonne par ordre croissant",
                            "sSortDescending": ": activer pour trier la colonne par ordre décroissant"
                        }
                    },
                    "order": tableConfig.order
                });
                console.log(`Table ${tableConfig.id} initialisée avec succès`);
            } catch (error) {
                console.error(`Erreur lors de l'initialisation de ${tableConfig.id}:`, error);
            }
        }
    });
}

// Initialiser au chargement du DOM
document.addEventListener('DOMContentLoaded', function() {
    // Attendre un peu pour s'assurer que jQuery et DataTables sont chargés
    setTimeout(function() {
        initializeReportsDataTables();
    }, 100);
});

// Fonction pour réinitialiser une table spécifique si nécessaire
window.reinitializeDataTable = function(tableId) {
    const table = $(tableId);
    if (table.length) {
        if ($.fn.DataTable.isDataTable(tableId)) {
            $(tableId).DataTable().destroy();
        }
        // Réinitialiser
        initializeReportsDataTables();
    }
};
